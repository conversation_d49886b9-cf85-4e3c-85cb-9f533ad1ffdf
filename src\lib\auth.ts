import { NextAuthOptions } from "next-auth";
import { JWT } from "next-auth/jwt";
import { Session } from "next-auth";

// 扩展 NextAuth 类型
declare module "next-auth" {
  interface Session {
    accessToken?: string;
    refreshToken?: string;
    idToken?: string;
    roles?: string[];
    hasRequiredRole?: boolean;
    error?: string;
  }

  interface User {
    accessToken?: string;
    refreshToken?: string;
    idToken?: string;
    roles?: string[];
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    accessToken?: string;
    refreshToken?: string;
    idToken?: string;
    roles?: string[];
    accessTokenExpires?: number;
    refreshTokenExpires?: number;
    error?: string;
  }
}

// 从 Keycloak JWT 中提取角色
function extractRoles(token: any): string[] {
  const roles: string[] = [];
  
  // 从 realm_access 中提取角色
  if (token.realm_access?.roles) {
    roles.push(...token.realm_access.roles);
  }
  
  // 从 resource_access 中提取特定客户端的角色
  const clientId = process.env.KEYCLOAK_ROLE_CLIENT_ID || process.env.KEYCLOAK_CLIENT_ID;
  if (clientId && token.resource_access?.[clientId]?.roles) {
    roles.push(...token.resource_access[clientId].roles);
  }
  
  return roles;
}

// 检查用户是否有必需的角色
function hasRequiredRole(roles: string[]): boolean {
  const requiredRole = process.env.KEYCLOAK_REQUIRED_ROLE;
  if (!requiredRole) return true;
  return roles.includes(requiredRole);
}

// 刷新访问令牌
async function refreshAccessToken(token: JWT): Promise<JWT> {
  try {
    const url = `${process.env.KEYCLOAK_ISSUER_URL}/protocol/openid-connect/token`;
    
    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        client_id: process.env.KEYCLOAK_CLIENT_ID!,
        client_secret: process.env.KEYCLOAK_CLIENT_SECRET!,
        grant_type: "refresh_token",
        refresh_token: token.refreshToken!,
      }),
      method: "POST",
    });

    const refreshedTokens = await response.json();

    if (!response.ok) {
      throw refreshedTokens;
    }

    // 解码新的访问令牌以获取角色信息
    const decodedToken = JSON.parse(
      Buffer.from(refreshedTokens.access_token.split('.')[1], 'base64').toString()
    );
    
    const roles = extractRoles(decodedToken);

    return {
      ...token,
      accessToken: refreshedTokens.access_token,
      accessTokenExpires: Date.now() + refreshedTokens.expires_in * 1000,
      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken,
      roles,
    };
  } catch (error) {
    console.error("Error refreshing access token:", error);
    return {
      ...token,
      error: "RefreshAccessTokenError",
    };
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    {
      id: "keycloak",
      name: "Keycloak",
      type: "oauth",
      wellKnown: `${process.env.KEYCLOAK_ISSUER_URL}/.well-known/openid_configuration`,
      authorization: {
        params: {
          scope: "openid email profile",
          response_type: "code",
          prompt: "login", // 强制重新登录以确保获取最新角色
        },
      },
      clientId: process.env.KEYCLOAK_CLIENT_ID,
      clientSecret: process.env.KEYCLOAK_CLIENT_SECRET,
      idToken: true,
      checks: ["pkce", "state"],
      profile(profile) {
        const roles = extractRoles(profile);
        return {
          id: profile.sub,
          name: profile.name ?? profile.preferred_username,
          email: profile.email,
          image: profile.picture,
          roles,
        };
      },
    },
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      // 初始登录
      if (account && user) {
        const decodedAccessToken = JSON.parse(
          Buffer.from(account.access_token!.split('.')[1], 'base64').toString()
        );
        
        const roles = extractRoles(decodedAccessToken);
        
        return {
          ...token,
          accessToken: account.access_token,
          refreshToken: account.refresh_token,
          idToken: account.id_token,
          accessTokenExpires: account.expires_at! * 1000,
          refreshTokenExpires: decodedAccessToken.exp * 1000,
          roles,
        };
      }

      // 返回之前的令牌如果访问令牌尚未过期
      if (Date.now() < (token.accessTokenExpires ?? 0)) {
        return token;
      }

      // 访问令牌已过期，尝试刷新
      return refreshAccessToken(token);
    },
    async session({ session, token }) {
      session.accessToken = token.accessToken;
      session.refreshToken = token.refreshToken;
      session.idToken = token.idToken;
      session.roles = token.roles || [];
      session.hasRequiredRole = hasRequiredRole(session.roles);
      session.error = token.error;

      return session;
    },
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  events: {
    async signOut({ token }) {
      // 可选：通知 Keycloak 用户已登出
      if (token?.idToken) {
        try {
          await fetch(
            `${process.env.KEYCLOAK_ISSUER_URL}/protocol/openid-connect/logout?id_token_hint=${token.idToken}&post_logout_redirect_uri=${encodeURIComponent(process.env.NEXTAUTH_URL!)}`
          );
        } catch (error) {
          console.error("Error during Keycloak logout:", error);
        }
      }
    },
  },
};
