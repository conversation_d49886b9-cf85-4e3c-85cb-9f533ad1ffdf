"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { signOut, useSession } from "next-auth/react";

interface EmailMessage {
  id: string;
  threadId: string;
  subject: string;
  from: string;
  to: string;
  date: string;
  snippet: string;
  body?: string;
  isRead: boolean;
}

interface EmailListProps {
  emails: EmailMessage[];
}

export default function EmailList({ emails }: EmailListProps) {
  const [selectedEmail, setSelectedEmail] = useState<EmailMessage | null>(null);
  const [emailBody, setEmailBody] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { data: session } = useSession();

  const loadEmailBody = async (emailId: string) => {
    try {
      const response = await fetch(`/api/emails/${emailId}`);
      const data = await response.json();

      if (data.success) {
        setEmailBody(data.body);
      }
    } catch (error) {
      console.error("Error loading email body:", error);
    }
  };

  const handleEmailClick = (email: EmailMessage) => {
    setSelectedEmail(email);
    loadEmailBody(email.id);
  };

  const handleRefresh = () => {
    setLoading(true);
    router.refresh();
    setTimeout(() => setLoading(false), 1000);
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: "/auth/signin" });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">One Mail</h1>
              {session?.user?.name && (
                <p className="text-sm text-gray-600 mt-1">
                  欢迎, {session.user.name}
                </p>
              )}
            </div>
            <div className="flex space-x-3">
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
              >
                {loading ? "Loading..." : "Refresh"}
              </button>
              <button
                onClick={handleSignOut}
                className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
              >
                登出
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Email List */}
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Emails ({emails.length})
                </h3>
              </div>
              <ul className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
                {emails.map((email) => (
                  <li
                    key={email.id}
                    className={`px-4 py-4 hover:bg-gray-50 cursor-pointer ${
                      selectedEmail?.id === email.id ? "bg-blue-50" : ""
                    }`}
                    onClick={() => handleEmailClick(email)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center">
                          <p
                            className={`text-sm font-medium text-gray-900 truncate ${
                              !email.isRead ? "font-bold" : ""
                            }`}
                          >
                            {email.subject}
                          </p>
                          {!email.isRead && (
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              New
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-500 truncate">
                          From: {email.from}
                        </p>
                        <p className="text-sm text-gray-400 truncate">
                          {email.snippet}
                        </p>
                      </div>
                      <div className="ml-2 flex-shrink-0 text-sm text-gray-500">
                        {new Date(email.date).toLocaleDateString()}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>

            {/* Email Detail */}
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              {selectedEmail ? (
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    {selectedEmail.subject}
                  </h3>
                  <div className="mb-4 text-sm text-gray-600">
                    <p>
                      <strong>From:</strong> {selectedEmail.from}
                    </p>
                    <p>
                      <strong>To:</strong> {selectedEmail.to}
                    </p>
                    <p>
                      <strong>Date:</strong>{" "}
                      {new Date(selectedEmail.date).toLocaleString()}
                    </p>
                  </div>
                  <div className="border-t pt-4">
                    <div
                      className="prose max-w-none text-sm"
                      dangerouslySetInnerHTML={{
                        __html: emailBody || selectedEmail.snippet,
                      }}
                    />
                  </div>
                </div>
              ) : (
                <div className="px-4 py-5 sm:p-6 text-center text-gray-500">
                  Select an email to view its content
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
