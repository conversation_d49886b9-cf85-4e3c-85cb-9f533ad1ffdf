import { redirect } from "next/navigation";

export default async function LoginPage() {
  // 使用 API 路由获取认证 URL，避免直接导入 googleapis
  try {
    const response = await fetch(
      `${
        process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:9042"
      }/api/auth/url`
    );
    const data = await response.json();

    if (data.success) {
      redirect(data.authUrl);
    } else {
      throw new Error(data.error || "Failed to get auth URL");
    }
  } catch (error) {
    console.error("Error getting auth URL:", error);
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Gmail Authentication Error
            </h2>
            <p className="mt-2 text-sm text-red-600">
              Failed to initialize authentication. Please try again.
            </p>
            <div className="mt-4">
              <a
                href="/"
                className="text-blue-600 hover:text-blue-500"
              >
                Go back
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
