import { redirect } from "next/navigation";

interface AuthCallbackProps {
  searchParams: { code?: string; error?: string };
}

export default async function AuthCallback({
  searchParams,
}: AuthCallbackProps) {
  const { code, error } = searchParams;

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Gmail Authentication
            </h2>
            <p className="mt-2 text-sm text-red-600">
              Authentication failed: {error}
            </p>
            <div className="mt-4">
              <a
                href="/auth/login"
                className="text-blue-600 hover:text-blue-500"
              >
                Try again
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (code) {
    // 使用 API 路由处理认证，避免在组件中直接导入 googleapis
    try {
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:9042"
        }/api/auth/callback`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ code }),
        }
      );

      const result = await response.json();

      if (result.success) {
        redirect("/");
      } else {
        return (
          <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="max-w-md w-full space-y-8">
              <div className="text-center">
                <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                  Gmail Authentication
                </h2>
                <p className="mt-2 text-sm text-red-600">
                  Authentication failed: {result.error || "Unknown error"}
                </p>
                <div className="mt-4">
                  <a
                    href="/auth/login"
                    className="text-blue-600 hover:text-blue-500"
                  >
                    Try again
                  </a>
                </div>
              </div>
            </div>
          </div>
        );
      }
    } catch (authError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full space-y-8">
            <div className="text-center">
              <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                Gmail Authentication
              </h2>
              <p className="mt-2 text-sm text-red-600">
                Authentication failed:{" "}
                {authError instanceof Error
                  ? authError.message
                  : "Unknown error"}
              </p>
              <div className="mt-4">
                <a
                  href="/auth/login"
                  className="text-blue-600 hover:text-blue-500"
                >
                  Try again
                </a>
              </div>
            </div>
          </div>
        </div>
      );
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Gmail Authentication
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            No authorization code received
          </p>
          <div className="mt-4">
            <a
              href="/auth/login"
              className="text-blue-600 hover:text-blue-500"
            >
              Try again
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
