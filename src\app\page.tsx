import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import EmailList from "@/components/EmailList";

interface EmailMessage {
  id: string;
  threadId: string;
  subject: string;
  from: string;
  to: string;
  date: string;
  snippet: string;
  body?: string;
  isRead: boolean;
}

async function getEmails(accessToken: string): Promise<{
  emails: EmailMessage[] | null;
  error?: string;
}> {
  try {
    // 使用内部 API 调用，传递访问令牌
    const response = await fetch(
      `${
        process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:9042"
      }/api/emails?maxResults=20`,
      {
        cache: "no-store", // 确保每次都获取最新数据
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error("Authentication required");
      }
      if (response.status === 403) {
        throw new Error("Insufficient permissions");
      }
      throw new Error("Failed to fetch emails");
    }

    const data = await response.json();

    if (data.success) {
      return { emails: data.messages };
    } else {
      return { emails: null, error: data.error || "Authentication required" };
    }
  } catch (error) {
    console.error("Error fetching emails:", error);
    return {
      emails: null,
      error: error instanceof Error ? error.message : "Authentication required",
    };
  }
}

export default async function Home(): Promise<JSX.Element> {
  const session = await getServerSession(authOptions);

  // 检查用户是否已登录
  if (!session) {
    redirect("/auth/signin");
  }

  // 检查会话是否有错误
  if (session.error === "RefreshAccessTokenError") {
    redirect("/auth/signin?error=session_expired");
  }

  // 检查用户是否有必需的角色
  if (!session.hasRequiredRole) {
    redirect("/auth/signin?error=insufficient_role");
  }

  // 获取邮件数据
  const { emails, error } = await getEmails(session.accessToken!);

  if (!emails || error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              One Mail
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              {error === "Insufficient permissions"
                ? "您没有访问邮件的权限"
                : "无法加载邮件数据"}
            </p>
          </div>
          <div className="mt-8 space-y-6">
            <a
              href="/auth/signin"
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              重新登录
            </a>
          </div>
        </div>
      </div>
    );
  }

  return <EmailList emails={emails} />;
}
