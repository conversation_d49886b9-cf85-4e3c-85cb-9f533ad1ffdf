import EmailList from "@/components/EmailList";

interface EmailMessage {
  id: string;
  threadId: string;
  subject: string;
  from: string;
  to: string;
  date: string;
  snippet: string;
  body?: string;
  isRead: boolean;
}

interface HomeProps {
  emails: EmailMessage[] | null;
  error?: string;
}

async function getEmails(): Promise<{
  emails: EmailMessage[] | null;
  error?: string;
}> {
  try {
    // 使用内部 API 调用，避免直接导入 googleapis
    const response = await fetch(
      `${
        process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:9042"
      }/api/emails?maxResults=20`,
      {
        cache: "no-store", // 确保每次都获取最新数据
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch emails");
    }

    const data = await response.json();

    if (data.success) {
      return { emails: data.messages };
    } else {
      return { emails: null, error: data.error || "Authentication required" };
    }
  } catch (error) {
    console.error("Error fetching emails:", error);
    return { emails: null, error: "Authentication required" };
  }
}

export default async function Home(): Promise<JSX.Element> {
  const { emails, error } = await getEmails();

  if (!emails || error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Gmail Reader
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Connect your Gmail account to read emails
            </p>
          </div>
          <div className="mt-8 space-y-6">
            <a
              href="/auth/login"
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Connect Gmail Account
            </a>
          </div>
        </div>
      </div>
    );
  }

  return <EmailList emails={emails} />;
}
